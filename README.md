# 智教云平台

智教云平台是一个面向教师和学生的智能辅导系统，提供作业上传、自动批改和错题强化训练等功能。

前端端口：3000
后端端口：8083
启动文件：根目录的 start_backend.bat 和 start_frontend.bat
数据库：backend/smart_edu.db

## 最近更新

### 2025年8月4日功能更新 🎉
**地区管理功能上线**
- 🗺️ **三级地区管理系统**：完整的省份、城市、区县三级地区数据管理
  - 34个省级行政区，392个城市，799个区县，数据完整覆盖全国
  - 标准行政区划代码：省份100%配置，主要城市和区县已配置标准代码
  - 三级联动功能：支持省→市→县的数据联动查看和跳转
- 🔐 **权限控制严格**：仅超级管理员可访问，确保数据安全
- 🛠️ **完整CRUD操作**：支持地区数据的增加、删除、修改、查询
- 📊 **数据标准化**：采用国家标准行政区划代码，数据规范统一
- 🔧 **批量更新工具**：提供脚本工具支持批量更新地区代码
- 详细功能说明请查看 [地区管理功能说明.md](./地区管理功能说明.md)

### 2025年8月1日重大更新 🎉
**📱 全平台移动端优化完成**
- 🎯 **全角色移动端支持**：学生、教师、管理员、超级管理员、家长五大角色完全移动端适配
- 📱 **响应式设计**：所有页面支持手机、平板、电脑多端访问，自适应屏幕尺寸
- 🎨 **移动端UI优化**：44px触摸目标、16px防缩放字体、圆角卡片设计、触摸反馈动画
- 🧩 **通用组件优化**：表格、表单、模态框、上传组件等全面移动端优化
- 📊 **数据展示优化**：移动端卡片式布局、简化分页、水平滚动支持
- 🔧 **交互体验优化**：大触摸区域、防误触设计、流畅滚动、直观操作反馈
- 详细优化说明请查看 [移动端优化文档](./移动端优化文档.md) 和 [移动端优化章节](#移动端优化)

**外网访问问题完全解决**
- 🌐 **花生壳内网穿透配置成功**：通过danphy.xicp.net:23277实现外网API访问
- 🔐 **学生权限系统正常**：学生账号可正常登录并查看自己的作业详情
- 📱 **跨域问题解决**：前端与后端API通信完全正常
- 🎯 **功能验证完成**：首页最近作业、已完成作业详情查看等功能全部正常
- 详细配置说明请查看 [外网部署配置](#外网部署配置) 章节

### 2025年8月1日功能更新 🎉
**拍照解题功能上线**
- 📸 **AI拍照解题**：学生可通过拍照或上传图片获得AI智能解题
  - 支持数学、语文、英语等多学科题目识别
  - 提供详细解题步骤、答案解析和知识点说明
  - 专业相机功能：实时预览、拍照指导、照片确认
  - 移动设备优化：完美支持手机拍照上传
- 🎯 **独立一级菜单**：拍照解题成为学生主要功能之一
- 📱 **多端适配**：响应式设计，支持手机、平板、电脑访问
- 🤖 **火山引擎AI**：集成GPT-4V视觉模型，识别准确率高
- 详细使用说明请查看 [拍照解题功能文档](./docs/PHOTO_SOLVE_FEATURE.md)

### 2025年7月31日重大更新 🎉
**多样化登录系统 & 批量导入功能完善**
- ✨ **全新多样化登录系统**：支持学校+用户名、手机号、邮箱三种登录方式
- 🏫 **分级学校选择**：省份→城市→区县→学校的完整选择体系，支持搜索功能
- 🔧 **批量导入功能完善**：教师、学生、家长批量导入全面优化
  - 科目映射问题完全修复，支持"初中语文"等带前缀科目
  - Excel模板完善，包含用户名、密码等完整字段
  - 密码显示修复，预览时显示实际密码而非掩码
  - 友好错误提示，详细显示具体错误原因和行号
- 👤 **用户名唯一性系统**：同一学校内所有角色用户名严格唯一
- 🗄️ **数据同步优化**：班级管理与系统班级管理数据完全同步
- 🔐 **权限系统完善**：角色权限检查逻辑优化，删除功能权限修复
- 📱 **用户体验优化**：登录界面现代化，支持常用学校记忆功能
- 详细信息请查看 [BUG修复记录.md](./BUG修复记录.md) 和 [功能说明文档.md](./功能说明文档.md)

### 2025年7月10日更新
- 修复了用户管理页面分页显示异常问题
- 优化了统计报表页面的班级筛选功能，支持自动应用筛选
- 改进了API响应数据格式处理，提高系统稳定性

## 主要功能

### 🔐 用户认证系统
- **多样化登录**：支持学校+用户名、手机号、邮箱三种登录方式
- **分级学校选择**：省份→城市→区县→学校的完整选择体系
- **智能搜索**：支持学校名称直接搜索，提升用户体验
- **权限管理**：基于学校和角色的精细化权限控制

### 📚 教学管理系统
- **作业管理**：支持批量上传与提交作业
- **智能批改**：接入AI大模型自动批改作业
- **📸 拍照解题**：学生专属AI智能解题功能
  - 多学科支持：数学、语文、英语、物理、化学等
  - 专业拍照：实时相机预览、拍照指导、照片确认
  - 详细解答：完整解题步骤、知识点解析、难度评估
  - 移动优化：完美支持手机拍照，响应式界面设计
- **错题强化**：基于错题自动生成个性化训练内容
- **数据分析**：提供详细的统计报表与分析

### 🏫 学校管理系统
- **多学校支持**：支持多学校部署和独立管理
- **🗺️ 地区管理**：完整的三级地区数据管理系统
  - 省份、城市、区县三级联动管理
  - 标准行政区划代码，数据规范统一
  - 仅超级管理员可访问，权限控制严格
  - 支持完整CRUD操作和批量更新
- **班级管理**：完整的教师、学生、家长管理功能
- **批量导入系统**：
  - Excel模板批量导入教师、学生、家长信息
  - 智能科目映射，支持带前缀科目（如"初中语文"）
  - 用户名唯一性检查，同校内严格唯一
  - 详细错误提示，精确定位问题行和原因
- **数据同步**：多页面数据实时同步，确保数据一致性
- **地理信息**：完整的省市区县地理信息支持

## 技术栈

- 前端：React + Ant Design + 响应式设计
- 后端：Python + FastAPI
- 数据库：SQLite (开发) / MySQL (生产)
- AI接口：支持火山引擎、本地ollama模型和通义千问等多种AI模型
- 网络：支持花生壳内网穿透，实现外网访问
- 移动端：CSS媒体查询 + JavaScript响应式适配

## 移动端优化

### 🎯 全角色移动端支持

#### 👨‍🎓 学生角色优化
- **作业管理**：移动端友好的作业列表、提交界面、详情查看
- **拍照解题**：专业相机界面、实时预览、触摸拍照
- **错题训练**：卡片式练习界面、大按钮操作、进度跟踪
- **学习统计**：图表适配、数据卡片、触摸交互

#### 👩‍🏫 教师角色优化
- **作业管理**：批量操作、快速批改、移动端表格
- **班级管理**：学生列表、成绩统计、家长沟通
- **统计分析**：图表展示、数据筛选、报告生成
- **系统管理**：用户管理、权限设置、数据导入

#### 👨‍💼 管理员角色优化
- **用户管理**：批量操作、权限分配、数据统计
- **学校管理**：多校管理、班级配置、系统设置
- **数据分析**：统计报表、趋势分析、导出功能
- **系统监控**：状态监控、日志查看、性能分析

#### 🔧 超级管理员优化
- **数据库管理**：SQL查询、表格浏览、数据统计
- **🗺️ 地区管理**：三级地区数据管理、标准代码配置、批量更新
- **系统设置**：全局配置、AI设置、权限管理
- **高级功能**：数据备份、系统维护、性能优化

#### 👨‍👩‍👧‍👦 家长角色优化
- **孩子监控**：学习进度、作业完成、成绩查看
- **家长报告**：详细分析、学习建议、进步趋势
- **家校沟通**：教师反馈、学习建议、互动交流

### 📱 移动端特性

#### 响应式设计
- **断点设置**：768px以下为移动端，自动切换布局
- **弹性布局**：Flexbox + Grid布局，适配各种屏幕
- **图片适配**：自动缩放、懒加载、格式优化
- **字体缩放**：16px最小字体，防止iOS自动缩放

#### 触摸优化
- **触摸目标**：44px最小触摸区域，符合移动端标准
- **触摸反馈**：按钮按压动画、视觉反馈效果
- **手势支持**：滑动、拖拽、双击等手势操作
- **防误触**：合理间距、清晰边界、确认机制

#### 性能优化
- **懒加载**：图片、组件按需加载，提升首屏速度
- **缓存策略**：静态资源缓存、API数据缓存
- **压缩优化**：代码压缩、图片压缩、资源合并
- **网络优化**：请求合并、数据预取、离线支持

#### 用户体验
- **加载状态**：骨架屏、进度条、加载动画
- **错误处理**：友好提示、重试机制、降级方案
- **无障碍**：语义化标签、键盘导航、屏幕阅读器支持
- **国际化**：多语言支持、本地化适配

### 🎨 UI组件优化

#### 通用组件
- **表格组件**：水平滚动、紧凑布局、触摸按钮
- **表单组件**：大输入框、防缩放、触摸友好
- **卡片组件**：圆角阴影、优化内边距、层次分明
- **模态框组件**：全屏适配、滚动优化、触摸关闭

#### 导航组件
- **标签页**：水平滚动、触摸切换、活跃状态
- **面包屑**：简化显示、触摸导航、层级清晰
- **菜单组件**：抽屉式菜单、分组显示、快速访问

#### 数据展示
- **统计卡片**：大数字显示、图标配色、触摸交互
- **图表组件**：响应式图表、触摸缩放、数据提示
- **列表组件**：卡片式布局、无限滚动、下拉刷新

#### 反馈组件
- **消息提示**：居中显示、自动消失、触摸关闭
- **确认对话框**：大按钮、清晰文案、触摸操作
- **加载组件**：骨架屏、进度指示、友好提示

## 外网部署配置

### 当前生产环境配置
- **前端访问域名**：17learn.cn
- **API访问地址**：http://danphy.xicp.net:23277/api
- **内网映射**：danphy.xicp.net:23277 → 192.168.0.104:8083
- **部署方式**：花生壳内网穿透
- **移动端支持**：完全支持手机、平板访问，响应式适配

### 配置文件
```bash
# frontend/.env
REACT_APP_API_URL=http://danphy.xicp.net:23277/api
PORT=3000
GENERATE_SOURCEMAP=false
REACT_APP_PHOTO_SOLVE_ENABLED=true
```

### 端口映射更新脚本
当花生壳分配新的动态端口时，使用以下脚本快速更新：
```bash
# Linux/Mac
./update-api-port.sh 新端口号

# Windows
update-api-port.bat 新端口号
```

## 开发说明

### 安装依赖

```bash
# 前端依赖
cd frontend
npm install

# 后端依赖
cd backend
pip install -r requirements.txt
```

### 运行项目

```bash
# 启动前端
cd frontend;npm run start


# 启动后端
cd backend;uvicorn main:app --reload --port 8083
```

# 学校管理系统 - 班级与学校关联方案

## 项目概述

本项目实现了学校管理系统中班级与学校的关联功能，解决了学生注册时选择学校后无法显示对应班级的问题。

## 实现方案

### 1. 学校类型映射

根据实际教育场景，将学校分为以下几种类型：
- `primary`: 小学（一至六年级）
- `middle`: 初中（七至九年级）
- `high`: 高中（高一至高三）
- `complete`: 完全中学（初中+高中）
- `complete_all`: 完全中学（小学+初中+高中）

### 2. 班级分配逻辑

- 每个学校根据其类型分配对应的班级
- 不同学校可以有相同名称的班级（如"七年级1班"），系统通过班级ID和学校ID的组合来唯一标识一个班级
- 学生注册时只能选择自己学校的班级
- 支持特定学校的定制班级分配方案（如成都双流中学、立格实验学校等）

### 3. 班级命名规则

- 小学班级：`{年级}{班号}班`，如"一年级1班"
- 初中班级：`{年级}{班号}班`，如"七年级2班" 
- 高中班级：`{年级}（{班号}）班`，如"高一（1）班"
- 支持特定学校的自定义命名规则

### 4. 主要脚本

- `school_type_mapping.py`: 定义学校类型映射和年级配置函数
- `assign_all_schools_classes.py`: 为所有学校分配班级的主脚本
- `assign_continuous_classes.py`: 连续分配班级的工具脚本
- `assign_continuous_classes_except_lige.py`: 排除特定学校的班级分配脚本
- `add_shuangliu_middle_classes.py`: 为成都双流中学添加专门的班级
- `fix_lige_middle_classes.py`: 修复立格实验学校班级命名和关联问题
- `check_schools_classes.py`: 检查学校班级分配情况
- `verify_school_classes.py`: 验证班级分配结果的正确性
- `check_db_stats.py`: 查询数据库统计信息

## 数据库结构

### 学校表 (schools)
- id: 主键
- name: 学校名称
- province: 省份
- city: 城市
- district: 区县
- is_active: 是否激活
- created_at: 创建时间
- school_type: 学校类型

### 班级表 (classes)
- id: 主键
- name: 班级名称
- grade: 年级
- school_id: 外键，关联到学校表
- created_at: 创建时间
- updated_at: 更新时间

## 数据库统计信息

- 学校总数: 46所
- 班级总数: 1359个
- 已分配学校的班级数量: 1350个
- 未分配学校的班级数量: 9个
- 有班级的学校数量: 46所（全部学校都有班级）
- 没有班级的学校数量: 0所

### 各年级班级数量
- 一年级: 96个班级
- 二年级: 96个班级
- 三年级: 96个班级
- 四年级: 96个班级
- 五年级: 96个班级
- 六年级: 96个班级
- 七年级: 138个班级
- 八年级: 138个班级
- 九年级: 138个班级
- 高一: 155个班级
- 高二: 155个班级
- 高三: 155个班级

### 班级数量最多的学校
1. 成都市树德中学: 42个班级
2. 成都立格实验学校: 39个班级
3. 重庆市第一中学: 30个班级
4. 成都外国语学校: 30个班级
5. 成都双流中学: 30个班级

## 班级分配结果

成功为多所学校分配了班级，包括：

1. 成都立格实验学校：完全中学（小学+初中+高中），共39个班级
   - 小学部分：一至六年级各3班
   - 初中部分：七至九年级各4班
   - 高中部分：高一至高三各3班

2. 成都市石室中学：完全中学（初中+高中），共21个班级
   - 初中部分：七至九年级各4班
   - 高中部分：高一至高三各3班

3. 成都市第七中学：高中，共15个班级
   - 高一至高三各5班

4. 成都市树德中学：完全中学（小学+初中+高中），共42个班级
   - 小学部分：一至六年级各3班
   - 初中部分：七至九年级各4班
   - 高中部分：高一至高三各4班

5. 成都双流中学：完全中学（初中+高中），共30个班级
   - 初中部分：七至九年级各5班
   - 高中部分：高一至高三各5班

6. 重庆市第一中学：完全中学（初中+高中），共30个班级
   - 初中部分：七至九年级各4班
   - 高中部分：高一至高三各6班

7. 北京市第四中学：高中，共18个班级
   - 高一至高三各6班

8. 北京市陈经纶中学：初中，共12个班级
   - 七至九年级各4班

## 使用方法

1. 运行学校类型映射脚本：
```bash
python school_type_mapping.py
```

2. 为所有学校分配班级：
```bash
python assign_all_schools_classes.py
```

3. 为特定学校分配班级：
```bash
python assign_all_schools_classes.py --school "学校名称" --primary-classes 3 --middle-classes 4 --high-classes 6
```

4. 检查班级分配情况：
```bash
python check_schools_classes.py
```

5. 验证班级分配结果：
```bash
python verify_school_classes.py
```

6. 查询数据库统计信息：
```bash
python check_db_stats.py
```

7. 修复特定学校班级问题：
```bash
# 修复立格实验学校班级
python fix_lige_middle_classes.py

# 为成都双流中学添加班级
python add_shuangliu_middle_classes.py
```

## 注意事项

- 班级分配基于学校类型，确保每个学校有连贯的年级结构
- 系统支持不同学校拥有相同名称的班级
- 班级与学校的关联通过school_id外键实现
- 特殊学校可能有定制的班级分配方案
- 验证脚本可以确保班级分配的正确性和完整性 