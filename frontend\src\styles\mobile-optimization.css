/**
 * 移动端优化样式
 * 专门解决移动端显示问题
 */

/* ===== 移动端状态标签优化 ===== */
@media (max-width: 768px) {
  /* StatusBadge 组件优化 */
  .student-status-badge {
    min-width: 70px !important; /* 确保能显示4个字 */
    text-align: center;
    white-space: nowrap;
    font-size: 13px !important;
    padding: 4px 10px !important;
    font-weight: 500 !important;
  }

  /* Ant Design Descriptions 优化 */
  .ant-descriptions-item {
    padding-bottom: 12px !important;
  }

  .ant-descriptions-item-label {
    min-width: 70px !important;
    width: auto !important;
    padding-right: 12px !important;
    font-weight: 500 !important;
    color: #666 !important;
  }

  .ant-descriptions-item-content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
  }

  /* 批改状态标签特别优化 */
  .ant-descriptions-item-content .student-status-badge,
  .ant-descriptions-item-content .ant-tag {
    min-width: 70px !important;
    text-align: center !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-weight: 500 !important;
  }

  /* 科目标签优化 */
  .ant-descriptions-item-content .ant-tag {
    padding: 4px 12px !important;
    border-radius: 6px !important;
  }
}

/* ===== 移动端内容区域优化 ===== */
@media (max-width: 768px) {
  /* 错误分析区域优化 */
  .ant-card .ant-typography {
    margin-bottom: 8px !important;
  }
  
  /* 减少卡片内边距 */
  .ant-card-body {
    padding: 16px 12px !important;
  }
  
  /* 优化段落间距 */
  .ant-typography p {
    margin-bottom: 8px !important;
  }
  
  /* 优化列表间距 */
  .ant-list-item {
    padding: 8px 0 !important;
  }
  
  /* 优化描述列表 */
  .ant-descriptions-item {
    padding-bottom: 8px !important;
  }
  
  /* 优化空间组件 */
  .ant-space-item {
    margin-right: 8px !important;
  }
}

/* ===== 超小屏幕优化 ===== */
@media (max-width: 576px) {
  /* 进一步减少内边距 */
  .ant-card-body {
    padding: 12px 8px !important;
  }

  /* 状态标签更紧凑但仍能显示4个字 */
  .student-status-badge {
    min-width: 68px !important; /* 确保"已批改"等4字能完整显示 */
    font-size: 12px !important;
    padding: 4px 8px !important;
    height: 26px !important;
    font-weight: 500 !important;
  }

  /* 描述列表更紧凑 */
  .ant-descriptions-item {
    padding-bottom: 8px !important;
  }

  .ant-descriptions-item-label {
    min-width: 65px !important; /* 确保标签文字完整显示 */
    font-size: 13px !important;
    font-weight: 500 !important;
  }

  .ant-descriptions-item-content {
    font-size: 13px !important;
  }

  /* 科目标签在小屏幕上的优化 */
  .ant-descriptions-item-content .ant-tag {
    min-width: 65px !important;
    font-size: 12px !important;
    padding: 3px 8px !important;
  }

  /* 段落文字优化 */
  .ant-typography {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* 列表项更紧凑 */
  .ant-list-item {
    padding: 6px 0 !important;
    font-size: 14px !important;
  }
}

/* ===== 学生作业详情页面特定优化 ===== */
@media (max-width: 768px) {
  /* 基本信息卡片优化 */
  .student-page .ant-card:first-child .ant-descriptions {
    margin-bottom: 8px !important;
  }
  
  /* 题号信息区域优化 */
  .student-page .ant-card .ant-row {
    margin-bottom: 8px !important;
  }
  
  /* 错误分析卡片优化 */
  .student-page .ant-card[style*="border: 2px solid #faad14"] .ant-card-body {
    padding: 10px !important;
  }

  /* 错误分析内容区域优化 */
  .student-page .ant-card[style*="border: 2px solid #faad14"] div[style*="background: rgba(250, 173, 20, 0.05)"] {
    padding: 10px !important;
    margin-bottom: 8px !important;
  }

  /* 强化建议内容区域优化 */
  .student-page .ant-card[style*="border: 2px solid #52c41a"] div[style*="background: rgba(82, 196, 26, 0.05)"] {
    padding: 10px !important;
    margin-bottom: 8px !important;
  }

  /* 强化建议卡片优化 */
  .student-page .ant-card[style*="border: 2px solid #faad14"] .ant-typography,
  .student-page .ant-card[style*="border: 2px solid #52c41a"] .ant-typography {
    margin-bottom: 6px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* 说明文字区域优化 */
  .student-page .ant-card div[style*="background: #fff7e6"],
  .student-page .ant-card div[style*="background: #f6f8fa"] {
    padding: 8px !important;
    margin-top: 8px !important;
    font-size: 11px !important;
    line-height: 1.4 !important;
  }
  
  /* 学习建议列表优化 */
  .student-page .ant-list-small .ant-list-item {
    padding: 6px 0 !important;
  }
  
  /* 分数显示优化 */
  .student-page .ant-col[style*="text-align: center"] {
    margin-bottom: 12px;
  }
  
  .student-page .ant-col[style*="text-align: center"] div:first-child {
    font-size: 28px !important;
    margin-bottom: 4px;
  }
}

/* ===== 横屏模式优化 ===== */
@media (max-width: 896px) and (orientation: landscape) {
  .student-status-badge {
    min-width: 55px !important;
    font-size: 12px !important;
    padding: 2px 6px !important;
    height: 22px !important;
  }
  
  .ant-card-body {
    padding: 10px 8px !important;
  }
  
  .ant-descriptions-item {
    padding-bottom: 4px !important;
  }
}

/* ===== 触摸设备优化 ===== */
@media (hover: none) and (pointer: coarse) {
  /* 增大触摸目标 */
  .student-status-badge {
    min-height: 32px !important;
    padding: 6px 10px !important;
  }
  
  /* 按钮触摸优化 */
  .ant-btn {
    min-height: 40px !important;
    padding: 8px 16px !important;
  }
  
  /* 链接触摸优化 */
  .ant-typography a {
    padding: 4px 0 !important;
    display: inline-block;
  }
}

/* ===== 文本选择优化 ===== */
@media (max-width: 768px) {
  /* 允许文本选择，方便复制 */
  .ant-typography,
  .ant-descriptions-item-content,
  .student-status-badge {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* 长文本换行优化 */
  .ant-typography p,
  .ant-descriptions-item-content {
    word-break: break-word;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

/* ===== 移动端图片预览优化 ===== */
@media (max-width: 768px) {
  /* 图片预览遮罩优化 */
  .student-image-mask {
    background: rgba(0, 0, 0, 0.6) !important;
    color: white !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
  }

  /* Ant Design Image 预览优化 */
  .ant-image-preview-root {
    z-index: 2000 !important;
  }

  .ant-image-preview-mask {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  .ant-image-preview-wrap {
    padding: 0 !important;
  }

  .ant-image-preview-body {
    padding: 0 !important;
  }

  /* 图片预览工具栏优化 */
  .ant-image-preview-operations {
    background: rgba(0, 0, 0, 0.7) !important;
    padding: 12px !important;
    border-radius: 8px !important;
    margin: 16px !important;
  }

  .ant-image-preview-operations .ant-image-preview-operations-operation {
    width: 44px !important;
    height: 44px !important;
    font-size: 18px !important;
    margin: 0 4px !important;
  }

  /* 图片容器优化 */
  .ant-image-preview-img-wrapper {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100vh !important;
    padding: 60px 16px !important;
    box-sizing: border-box !important;
  }

  .ant-image-preview-img {
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    touch-action: pan-x pan-y pinch-zoom !important;
    user-select: none !important;
    -webkit-user-drag: none !important;
  }

  /* 关闭按钮优化 */
  .ant-image-preview-close {
    width: 48px !important;
    height: 48px !important;
    font-size: 20px !important;
    top: 16px !important;
    right: 16px !important;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 24px !important;
    color: white !important;
    border: none !important;
  }

  .ant-image-preview-close:hover {
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
  }
}

/* ===== 图片网格布局优化 ===== */
@media (max-width: 768px) {
  /* 移动端图片网格单列显示 */
  .student-page div[style*="grid-template-columns"] {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  /* 图片卡片优化 */
  .student-page .ant-card.ant-card-hoverable {
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .student-page .ant-card.ant-card-hoverable:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  /* 图片标题优化 */
  .student-page .ant-card div[style*="text-align: center"] {
    font-weight: 500 !important;
    color: #4A90E2 !important;
  }
}

/* ===== 标签页优化 ===== */
@media (max-width: 768px) {
  /* 标签页头部优化 */
  .ant-tabs-tab {
    padding: 12px 8px !important;
    font-size: 14px !important;
  }

  .ant-tabs-tab-btn {
    font-weight: 500 !important;
  }

  .ant-tabs-content-holder {
    padding: 0 !important;
  }

  .ant-tabs-tabpane {
    padding: 16px 0 !important;
  }
}

/* ===== 高分辨率屏幕优化 ===== */
@media (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px) {
  .student-status-badge {
    border-width: 0.5px !important;
  }

  .ant-card {
    border-width: 0.5px !important;
  }

  .ant-descriptions-item {
    border-bottom-width: 0.5px !important;
  }

  .ant-image-preview-operations .ant-image-preview-operations-operation {
    border-width: 0.5px !important;
  }
}

/* ===== 微信浏览器特殊优化 ===== */
@media (max-width: 768px) {
  /* 微信浏览器图片预览优化 */
  .ant-image-preview-root {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }

  /* 防止微信浏览器的下拉刷新 */
  .ant-image-preview-wrap {
    overflow: hidden !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 图片预览时禁用页面滚动 */
  body.ant-scrolling-effect {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
  }
}

/* ===== 触摸设备图片交互优化 ===== */
@media (hover: none) and (pointer: coarse) {
  /* 图片卡片触摸反馈 */
  .student-page .ant-card.ant-card-hoverable {
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
  }

  .student-page .ant-card.ant-card-hoverable:active {
    transform: scale(0.98) !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
  }

  /* 图片预览按钮触摸优化 */
  .ant-image-preview-operations .ant-image-preview-operations-operation {
    min-width: 48px !important;
    min-height: 48px !important;
    touch-action: manipulation !important;
  }

  .ant-image-preview-operations .ant-image-preview-operations-operation:active {
    transform: scale(0.95) !important;
    background: rgba(255, 255, 255, 0.3) !important;
  }

  /* 关闭按钮触摸优化 */
  .ant-image-preview-close {
    min-width: 48px !important;
    min-height: 48px !important;
    touch-action: manipulation !important;
  }

  .ant-image-preview-close:active {
    transform: scale(0.95) !important;
    background: rgba(0, 0, 0, 0.9) !important;
  }
}

/* ===== AI助手移动端优化 ===== */
@media (max-width: 768px) {
  /* AI助手抽屉优化 */
  .ant-drawer-content-wrapper {
    width: 100% !important;
  }

  .ant-drawer-body {
    padding: 16px 12px !important;
  }

  .ant-drawer-header {
    padding: 16px 12px !important;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-drawer-title {
    font-size: 16px !important;
    font-weight: 600 !important;
  }

  .ant-drawer-close {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
    top: 12px !important;
    right: 12px !important;
  }

  /* AI聊天界面优化 */
  .chat-container {
    height: calc(100vh - 120px) !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .chat-messages {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 8px 0 !important;
    margin-bottom: 12px !important;
  }

  .chat-input {
    padding: 12px 0 !important;
    border-top: 1px solid #f0f0f0 !important;
    background: white !important;
    position: sticky !important;
    bottom: 0 !important;
  }

  .chat-input-field {
    font-size: 16px !important; /* 防止iOS缩放 */
    padding: 12px !important;
    border-radius: 8px !important;
  }

  .chat-input .ant-btn {
    height: 44px !important;
    padding: 0 16px !important;
    font-size: 14px !important;
    margin-left: 8px !important;
  }

  /* 聊天消息气泡优化 */
  .chat-message {
    margin-bottom: 12px !important;
    padding: 8px 12px !important;
    border-radius: 12px !important;
    max-width: 85% !important;
    word-wrap: break-word !important;
    line-height: 1.5 !important;
  }

  .chat-message.user {
    background: #1890ff !important;
    color: white !important;
    margin-left: auto !important;
    margin-right: 0 !important;
  }

  .chat-message.assistant {
    background: #f6f6f6 !important;
    color: #333 !important;
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  /* 加载状态优化 */
  .chat-loading {
    text-align: center !important;
    padding: 16px !important;
  }

  .chat-loading .ant-spin-text {
    font-size: 14px !important;
    color: #666 !important;
  }
}

/* ===== 首页立即咨询按钮优化 ===== */
@media (max-width: 768px) {
  /* 立即咨询按钮触摸优化 */
  .ant-card .ant-btn-primary.ant-btn-lg {
    min-height: 48px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    touch-action: manipulation !important;
  }

  .ant-card .ant-btn-primary.ant-btn-lg:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* AI助手提示卡片优化 */
  .ant-card[style*="background: #f0f7ff"] {
    margin: 20px 0 !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
  }

  .ant-card[style*="background: #f0f7ff"] .ant-card-body {
    padding: 16px !important;
  }

  .ant-card[style*="background: #f0f7ff"] .ant-typography h4 {
    font-size: 16px !important;
    margin-bottom: 8px !important;
    color: #1890ff !important;
  }

  .ant-card[style*="background: #f0f7ff"] .ant-typography p {
    font-size: 14px !important;
    line-height: 1.5 !important;
    margin-bottom: 12px !important;
    color: #666 !important;
  }
}

/* ===== 微信浏览器AI助手优化 ===== */
@media (max-width: 768px) {
  /* 微信浏览器特殊处理 */
  .ant-drawer-mask {
    background: rgba(0, 0, 0, 0.6) !important;
  }

  /* 防止微信浏览器的下拉刷新 */
  .ant-drawer-content {
    overflow: hidden !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 输入框焦点时的处理 */
  .chat-input-field:focus {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  }

  /* 键盘弹起时的适配 */
  @supports (-webkit-touch-callout: none) {
    .chat-container {
      height: calc(100vh - 140px) !important;
    }

    .chat-input {
      padding-bottom: env(safe-area-inset-bottom) !important;
    }
  }
}

/* ===== 移动端侧边栏菜单优化 ===== */
@media (max-width: 768px) {
  /* 抽屉式菜单优化 */
  .ant-drawer-content-wrapper {
    max-width: 280px !important;
  }

  .ant-drawer-header {
    padding: 16px 20px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .ant-drawer-title {
    color: white !important;
    font-size: 18px !important;
    font-weight: 600 !important;
  }

  .ant-drawer-close {
    color: white !important;
    font-size: 18px !important;
    width: 40px !important;
    height: 40px !important;
  }

  .ant-drawer-close:hover {
    color: #1890ff !important;
    background: rgba(255, 255, 255, 0.1) !important;
  }

  /* 移动端菜单项优化 */
  .ant-drawer .ant-menu-dark {
    background: transparent !important;
  }

  .ant-drawer .ant-menu-dark .ant-menu-item {
    height: 56px !important;
    line-height: 56px !important;
    margin: 0 !important;
    padding: 0 20px !important;
    border-radius: 0 !important;
    font-size: 16px !important;
    color: rgba(255, 255, 255, 0.85) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
  }

  .ant-drawer .ant-menu-dark .ant-menu-item:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    color: white !important;
  }

  .ant-drawer .ant-menu-dark .ant-menu-item-selected {
    background: #1890ff !important;
    color: white !important;
  }

  .ant-drawer .ant-menu-dark .ant-menu-item-selected::after {
    display: none !important;
  }

  .ant-drawer .ant-menu-dark .ant-menu-item .anticon {
    font-size: 18px !important;
    margin-right: 12px !important;
    color: inherit !important;
  }

  /* 菜单触发按钮优化 */
  .ant-layout-header .ant-btn[aria-label*="菜单"] {
    width: 48px !important;
    height: 48px !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    color: #666 !important;
    border: none !important;
    background: rgba(0, 0, 0, 0.04) !important;
  }

  .ant-layout-header .ant-btn[aria-label*="菜单"]:hover {
    background: rgba(0, 0, 0, 0.08) !important;
    color: #1890ff !important;
  }

  .ant-layout-header .ant-btn[aria-label*="菜单"]:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }
}

/* ===== 移动端菜单动画优化 ===== */
@media (max-width: 768px) {
  /* 抽屉动画优化 */
  .ant-drawer {
    z-index: 1050 !important;
  }

  .ant-drawer-mask {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px) !important;
  }

  .ant-drawer-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* 菜单项点击反馈 */
  .ant-drawer .ant-menu-dark .ant-menu-item:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* 防止菜单滚动时的问题 */
  .ant-drawer-body {
    padding: 0 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 安全区域适配 */
  @supports (padding: max(0px)) {
    .ant-drawer-content {
      padding-top: max(0px, env(safe-area-inset-top)) !important;
    }

    .ant-drawer-body {
      padding-bottom: max(16px, env(safe-area-inset-bottom)) !important;
    }
  }
}

/* ===== 微信浏览器菜单优化 ===== */
@media (max-width: 768px) {
  /* 微信浏览器特殊处理 */
  .ant-drawer-content-wrapper {
    height: 100vh !important;
    max-height: 100vh !important;
  }

  /* 防止微信浏览器的橡皮筋效果 */
  .ant-drawer-body {
    position: relative !important;
    overflow: hidden !important;
  }

  .ant-drawer .ant-menu {
    height: 100% !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* iOS Safari 特殊处理 */
  @supports (-webkit-touch-callout: none) {
    .ant-drawer-content {
      height: 100vh !important;
      height: -webkit-fill-available !important;
    }
  }
}

/* ===== 作业点评页面移动端优化 ===== */
@media (max-width: 768px) {
  /* 作业点评表格优化 */
  .student-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .student-page .ant-table {
    min-width: 800px !important; /* 确保表格有最小宽度 */
  }

  .student-page .ant-table-thead > tr > th {
    padding: 12px 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
  }

  .student-page .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 14px !important;
    vertical-align: middle !important;
  }

  /* 作业点评按钮优化 */
  .student-page .ant-table-tbody .ant-btn {
    min-width: 80px !important;
    height: 36px !important;
    font-size: 13px !important;
    padding: 0 12px !important;
    border-radius: 6px !important;
    touch-action: manipulation !important;
    white-space: nowrap !important;
  }

  .student-page .ant-table-tbody .ant-btn:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }

  .student-page .ant-table-tbody .ant-btn-primary {
    background: #1890ff !important;
    border-color: #1890ff !important;
  }

  .student-page .ant-table-tbody .ant-btn-primary:hover {
    background: #40a9ff !important;
    border-color: #40a9ff !important;
  }

  .student-page .ant-table-tbody .ant-btn:disabled {
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #bfbfbf !important;
  }

  /* 表格标签优化 */
  .student-page .ant-tag {
    margin: 0 !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
  }

  /* 表格分页优化 */
  .student-page .ant-pagination {
    margin: 16px 0 !important;
    text-align: center !important;
  }

  .student-page .ant-pagination-item,
  .student-page .ant-pagination-prev,
  .student-page .ant-pagination-next {
    min-width: 40px !important;
    height: 40px !important;
    line-height: 38px !important;
    margin: 0 4px !important;
  }

  .student-page .ant-pagination-options {
    display: none !important; /* 移动端隐藏分页选项 */
  }
}

/* ===== 作业点评筛选器移动端优化 ===== */
@media (max-width: 768px) {
  /* 筛选卡片优化 */
  .student-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 8px !important;
  }

  .student-page .ant-card-body {
    padding: 16px !important;
  }

  /* 筛选器行优化 */
  .student-page .ant-row {
    margin: 0 -8px !important;
  }

  .student-page .ant-col {
    padding: 0 8px !important;
    margin-bottom: 12px !important;
  }

  /* 筛选器选择框优化 */
  .student-page .ant-select {
    width: 100% !important;
    min-height: 44px !important; /* 增大触摸目标 */
  }

  .student-page .ant-select-selector {
    height: 44px !important;
    padding: 8px 12px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 6px !important;
  }

  .student-page .ant-select-selection-item {
    line-height: 28px !important;
  }

  /* 日期选择器优化 */
  .student-page .ant-picker {
    width: 100% !important;
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
  }

  .student-page .ant-picker-input > input {
    font-size: 16px !important;
    height: 42px !important;
  }

  /* 重置按钮优化 */
  .student-page .ant-btn {
    height: 44px !important;
    padding: 0 16px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
    touch-action: manipulation !important;
  }

  .student-page .ant-btn:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

/* ===== 作业点评表格响应式优化 ===== */
@media (max-width: 768px) {
  /* 表格容器滚动优化 */
  .student-page .ant-table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 表格固定列优化 */
  .student-page .ant-table-thead > tr > th:first-child,
  .student-page .ant-table-tbody > tr > td:first-child {
    position: sticky !important;
    left: 0 !important;
    z-index: 1 !important;
    background: white !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
  }

  .student-page .ant-table-thead > tr > th:first-child {
    background: #fafafa !important;
  }

  /* 操作列固定在右侧 */
  .student-page .ant-table-thead > tr > th:last-child,
  .student-page .ant-table-tbody > tr > td:last-child {
    position: sticky !important;
    right: 0 !important;
    z-index: 1 !important;
    background: white !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
  }

  .student-page .ant-table-thead > tr > th:last-child {
    background: #fafafa !important;
  }

  /* 表格行高优化 */
  .student-page .ant-table-tbody > tr {
    height: 60px !important;
  }

  /* 空状态优化 */
  .student-page .ant-empty {
    padding: 40px 16px !important;
  }

  .student-page .ant-empty-description {
    font-size: 14px !important;
    color: #666 !important;
  }
}

/* ===== 微信浏览器作业点评优化 ===== */
@media (max-width: 768px) {
  /* 微信浏览器表格优化 */
  .student-page .ant-table-wrapper {
    border-radius: 8px !important;
    overflow: hidden !important;
  }

  /* 防止微信浏览器的橡皮筋效果 */
  .student-page .ant-table-body {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 按钮点击区域优化 */
  .student-page .ant-table-tbody .ant-btn,
  .student-page .ant-list .ant-btn {
    position: relative !important;
    z-index: 10 !important;
    pointer-events: auto !important;
  }

  .student-page .ant-table-tbody .ant-btn::before,
  .student-page .ant-list .ant-btn::before {
    content: '' !important;
    position: absolute !important;
    top: -8px !important;
    left: -8px !important;
    right: -8px !important;
    bottom: -8px !important;
    z-index: -1 !important;
  }

  /* 移动端卡片按钮特殊优化 */
  .student-page .ant-list-item .ant-card {
    pointer-events: auto !important;
  }

  .student-page .ant-list-item .ant-btn {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
    tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
  }

  .student-page .ant-list-item .ant-btn:active {
    background: #40a9ff !important;
    transform: scale(0.95) !important;
    transition: all 0.1s ease !important;
  }

  .student-page .ant-list-item .ant-btn:disabled:active {
    background: #d9d9d9 !important;
    transform: none !important;
  }

  /* 表格滚动指示器 */
  .student-page .ant-table-wrapper::after {
    content: '← 左右滑动查看更多 →' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
    font-size: 12px !important;
    color: #999 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    padding: 4px !important;
    border-top: 1px solid #f0f0f0 !important;
  }
}

/* ===== 教师角色移动端优化 ===== */
@media (max-width: 768px) {
  /* 教师作业管理页面优化 */
  .teacher-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .teacher-page .ant-table {
    min-width: 900px !important; /* 教师表格内容更多，需要更大最小宽度 */
  }

  .teacher-page .ant-table-thead > tr > th {
    padding: 12px 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    background: #fafafa !important;
  }

  .teacher-page .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 14px !important;
    vertical-align: middle !important;
  }

  /* 教师操作按钮优化 */
  .teacher-page .ant-table-tbody .ant-btn {
    min-width: 72px !important;
    height: 36px !important;
    font-size: 13px !important;
    padding: 0 12px !important;
    border-radius: 6px !important;
    touch-action: manipulation !important;
    white-space: nowrap !important;
    margin: 2px !important;
  }

  .teacher-page .ant-table-tbody .ant-btn:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }

  .teacher-page .ant-table-tbody .ant-btn-primary {
    background: #1890ff !important;
    border-color: #1890ff !important;
  }

  .teacher-page .ant-table-tbody .ant-btn-danger {
    background: #ff4d4f !important;
    border-color: #ff4d4f !important;
  }

  .teacher-page .ant-table-tbody .ant-btn-default {
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #595959 !important;
  }

  /* 教师页面标签优化 */
  .teacher-page .ant-tag {
    margin: 0 2px !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
  }

  /* 教师页面分页优化 */
  .teacher-page .ant-pagination {
    margin: 16px 0 !important;
    text-align: center !important;
  }

  .teacher-page .ant-pagination-item,
  .teacher-page .ant-pagination-prev,
  .teacher-page .ant-pagination-next {
    min-width: 40px !important;
    height: 40px !important;
    line-height: 38px !important;
    margin: 0 4px !important;
  }

  .teacher-page .ant-pagination-options {
    display: none !important; /* 移动端隐藏分页选项 */
  }
}

/* ===== 教师筛选器移动端优化 ===== */
@media (max-width: 768px) {
  /* 教师筛选卡片优化 */
  .teacher-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 8px !important;
  }

  .teacher-page .ant-card-body {
    padding: 16px !important;
  }

  /* 教师筛选器行优化 */
  .teacher-page .ant-row {
    margin: 0 -8px !important;
  }

  .teacher-page .ant-col {
    padding: 0 8px !important;
    margin-bottom: 12px !important;
  }

  /* 教师筛选器选择框优化 */
  .teacher-page .ant-select {
    width: 100% !important;
    min-height: 44px !important; /* 增大触摸目标 */
  }

  .teacher-page .ant-select-selector {
    height: 44px !important;
    padding: 8px 12px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 6px !important;
  }

  .teacher-page .ant-select-selection-item {
    line-height: 28px !important;
  }

  /* 教师日期选择器优化 */
  .teacher-page .ant-picker {
    width: 100% !important;
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
  }

  .teacher-page .ant-picker-input > input {
    font-size: 16px !important;
    height: 42px !important;
  }

  /* 教师操作按钮优化 */
  .teacher-page .ant-btn {
    height: 44px !important;
    padding: 0 16px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
    touch-action: manipulation !important;
  }

  .teacher-page .ant-btn:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* 教师批量操作按钮 */
  .teacher-page .ant-btn-group .ant-btn {
    height: 40px !important;
    font-size: 14px !important;
    padding: 0 12px !important;
  }
}

/* ===== 教师统计页面移动端优化 ===== */
@media (max-width: 768px) {
  /* 教师统计卡片优化 */
  .teacher-page .ant-statistic-card {
    margin-bottom: 16px !important;
    border-radius: 8px !important;
  }

  .teacher-page .ant-statistic {
    text-align: center !important;
  }

  .teacher-page .ant-statistic-title {
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }

  .teacher-page .ant-statistic-content {
    font-size: 24px !important;
    font-weight: 600 !important;
  }

  /* 教师图表容器优化 */
  .teacher-page .chart-container {
    height: 300px !important;
    margin-bottom: 16px !important;
  }

  /* 教师数据表格优化 */
  .teacher-page .data-table {
    font-size: 14px !important;
  }

  .teacher-page .data-table .ant-table-thead > tr > th {
    font-size: 13px !important;
    padding: 8px 6px !important;
  }

  .teacher-page .data-table .ant-table-tbody > tr > td {
    font-size: 13px !important;
    padding: 8px 6px !important;
  }
}

/* ===== 教师作业批改移动端优化 ===== */
@media (max-width: 768px) {
  /* 作业批改页面优化 */
  .homework-grading-page {
    padding: 12px !important;
  }

  .homework-grading-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 8px !important;
  }

  .homework-grading-page .ant-card-body {
    padding: 16px !important;
  }

  /* 作业图片查看优化 */
  .homework-grading-page .homework-images {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .homework-grading-page .homework-image-card {
    border-radius: 8px !important;
    overflow: hidden !important;
  }

  .homework-grading-page .homework-image {
    width: 100% !important;
    height: auto !important;
    max-height: 400px !important;
    object-fit: contain !important;
  }

  /* 批改工具栏优化 */
  .homework-grading-page .grading-toolbar {
    position: sticky !important;
    bottom: 0 !important;
    background: white !important;
    padding: 12px !important;
    border-top: 1px solid #f0f0f0 !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .homework-grading-page .grading-toolbar .ant-btn {
    height: 44px !important;
    font-size: 16px !important;
    margin: 0 4px !important;
  }

  /* 评分输入框优化 */
  .homework-grading-page .score-input {
    height: 44px !important;
    font-size: 18px !important;
    text-align: center !important;
    border-radius: 8px !important;
  }

  /* 评语输入框优化 */
  .homework-grading-page .comment-textarea {
    min-height: 100px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    resize: vertical !important;
  }
}

/* ===== 教师班级管理移动端优化 ===== */
@media (max-width: 768px) {
  /* 班级管理页面优化 */
  .class-management-page {
    padding: 12px !important;
  }

  .class-management-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 8px !important;
  }

  /* 学生列表卡片式布局 */
  .class-management-page .student-list {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .class-management-page .student-card {
    padding: 16px !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 8px !important;
    background: white !important;
  }

  .class-management-page .student-card:active {
    background: #f8f9fa !important;
    transform: scale(0.98) !important;
    transition: all 0.1s ease !important;
  }

  .class-management-page .student-info {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
  }

  .class-management-page .student-name {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
  }

  .class-management-page .student-actions {
    display: flex !important;
    gap: 8px !important;
  }

  .class-management-page .student-actions .ant-btn {
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
  }
}

/* ===== 管理员角色移动端优化 ===== */
@media (max-width: 768px) {
  /* 管理员仪表板页面优化 */
  .admin-page {
    padding: 12px 4px !important;
  }

  .admin-page .ant-layout {
    background: transparent !important;
  }

  .admin-page .ant-layout-content {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 管理员标签页优化 */
  .admin-page .ant-tabs {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
  }

  .admin-page .ant-tabs-tab {
    padding: 12px 8px !important;
    font-size: 13px !important;
    margin: 0 !important;
  }

  .admin-page .ant-tabs-tab-btn {
    font-weight: 500 !important;
    white-space: nowrap !important;
  }

  .admin-page .ant-tabs-content-holder {
    padding: 0 !important;
  }

  .admin-page .ant-tabs-tabpane {
    padding: 16px !important;
  }

  /* 管理员表格优化 */
  .admin-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .admin-page .ant-table {
    min-width: 800px !important;
  }

  .admin-page .ant-table-thead > tr > th {
    padding: 12px 8px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    background: #fafafa !important;
  }

  .admin-page .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 13px !important;
    vertical-align: middle !important;
  }

  /* 管理员操作按钮优化 */
  .admin-page .ant-table-tbody .ant-btn {
    min-width: 60px !important;
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    border-radius: 4px !important;
    touch-action: manipulation !important;
    margin: 1px !important;
  }

  .admin-page .ant-table-tbody .ant-btn:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }

  /* 管理员表单优化 */
  .admin-page .ant-form-item {
    margin-bottom: 16px !important;
  }

  .admin-page .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .admin-page .ant-form-item-label > label {
    font-size: 14px !important;
    font-weight: 500 !important;
  }

  .admin-page .ant-input,
  .admin-page .ant-select-selector,
  .admin-page .ant-picker {
    height: 44px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 6px !important;
  }

  .admin-page .ant-input-affix-wrapper {
    height: 44px !important;
    font-size: 16px !important;
  }

  .admin-page .ant-select-selection-item {
    line-height: 42px !important;
  }

  /* 管理员模态框优化 */
  .admin-page .ant-modal {
    margin: 0 !important;
    max-width: calc(100vw - 16px) !important;
  }

  .admin-page .ant-modal-content {
    border-radius: 12px !important;
  }

  .admin-page .ant-modal-header {
    padding: 16px 20px !important;
    border-radius: 12px 12px 0 0 !important;
  }

  .admin-page .ant-modal-body {
    padding: 20px !important;
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
  }

  .admin-page .ant-modal-footer {
    padding: 12px 20px !important;
    text-align: center !important;
  }

  .admin-page .ant-modal-footer .ant-btn {
    height: 44px !important;
    font-size: 16px !important;
    padding: 0 24px !important;
    margin: 0 8px !important;
  }
}

/* ===== 管理员用户管理移动端优化 ===== */
@media (max-width: 768px) {
  /* 用户管理页面优化 */
  .user-management-page {
    padding: 12px 4px !important;
  }

  .user-management-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
  }

  .user-management-page .ant-card-body {
    padding: 16px !important;
  }

  /* 用户搜索栏优化 */
  .user-management-page .search-bar {
    margin-bottom: 16px !important;
  }

  .user-management-page .search-bar .ant-input-search {
    height: 44px !important;
  }

  .user-management-page .search-bar .ant-input-search .ant-input {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px 0 0 6px !important;
  }

  .user-management-page .search-bar .ant-btn {
    height: 44px !important;
    border-radius: 0 6px 6px 0 !important;
  }

  /* 用户筛选器优化 */
  .user-management-page .filter-row {
    margin-bottom: 16px !important;
  }

  .user-management-page .filter-row .ant-col {
    margin-bottom: 12px !important;
  }

  .user-management-page .filter-row .ant-select {
    width: 100% !important;
    height: 44px !important;
  }

  .user-management-page .filter-row .ant-select-selector {
    height: 44px !important;
    font-size: 16px !important;
  }

  /* 用户卡片式布局 */
  .user-management-page .user-list {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .user-management-page .user-card {
    padding: 16px !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .user-management-page .user-card:active {
    background: #f8f9fa !important;
    transform: scale(0.98) !important;
    transition: all 0.1s ease !important;
  }

  .user-management-page .user-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 12px !important;
  }

  .user-management-page .user-info {
    flex: 1 !important;
  }

  .user-management-page .user-name {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 4px !important;
  }

  .user-management-page .user-role {
    font-size: 13px !important;
    color: #666 !important;
  }

  .user-management-page .user-status {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
  }

  .user-management-page .user-actions {
    display: flex !important;
    gap: 8px !important;
    margin-top: 12px !important;
    flex-wrap: wrap !important;
  }

  .user-management-page .user-actions .ant-btn {
    height: 36px !important;
    font-size: 13px !important;
    padding: 0 12px !important;
    border-radius: 6px !important;
    flex: 1 !important;
    min-width: 80px !important;
  }

  /* 用户详情优化 */
  .user-management-page .user-details {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 8px !important;
    margin-top: 8px !important;
    padding-top: 8px !important;
    border-top: 1px solid #f0f0f0 !important;
  }

  .user-management-page .user-detail-item {
    font-size: 12px !important;
  }

  .user-management-page .user-detail-label {
    color: #8c8c8c !important;
    margin-bottom: 2px !important;
  }

  .user-management-page .user-detail-value {
    color: #595959 !important;
    font-weight: 500 !important;
  }
}

/* ===== 超级管理员移动端优化 ===== */
@media (max-width: 768px) {
  /* 超级管理员通用页面优化 */
  .super-admin-page,
  .system-page,
  .admin-system-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .super-admin-page .ant-layout,
  .system-page .ant-layout,
  .admin-system-page .ant-layout {
    background: transparent !important;
  }

  .super-admin-page .ant-layout-content,
  .system-page .ant-layout-content,
  .admin-system-page .ant-layout-content {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 超级管理员页面标题优化 */
  .super-admin-page .page-header,
  .system-page .page-header,
  .admin-system-page .page-header {
    margin-bottom: 16px !important;
    padding: 16px !important;
    background: white !important;
    border-radius: 12px !important;
    text-align: center !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .super-admin-page .page-header h1,
  .system-page .page-header h1,
  .admin-system-page .page-header h1 {
    margin: 0 !important;
    font-size: 18px !important;
    color: #1890ff !important;
    font-weight: 600 !important;
  }

  /* 数据库管理页面优化 */
  .db-admin-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .db-admin-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .db-admin-page .ant-card-body {
    padding: 16px !important;
  }

  /* SQL查询编辑器优化 */
  .db-admin-page .sql-editor {
    margin-bottom: 16px !important;
  }

  .db-admin-page .sql-editor .ant-input {
    min-height: 120px !important;
    font-size: 14px !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    border-radius: 8px !important;
  }

  .db-admin-page .sql-editor .ant-btn {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    margin: 4px !important;
  }

  /* 系统作业管理页面优化 */
  .system-homework-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-homework-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-homework-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .system-homework-page .ant-table {
    min-width: 900px !important;
  }

  /* 系统用户管理页面优化 */
  .system-user-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-user-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-user-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .system-user-page .ant-table {
    min-width: 800px !important;
  }

  /* 系统班级管理页面优化 */
  .system-class-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-class-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-class-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .system-class-page .ant-table {
    min-width: 850px !important;
  }

  /* 系统班级管理表格专项优化 */
  .system-class-page .ant-table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* 班级列表表格 */
  .system-class-page .ant-table-thead > tr > th {
    padding: 8px 6px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    min-width: 80px !important;
  }

  .system-class-page .ant-table-tbody > tr > td {
    padding: 8px 6px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
  }

  /* 学生表格优化 */
  .system-class-page .ant-tabs-tabpane[data-node-key="students"] .ant-table {
    min-width: 600px !important;
  }

  /* 教师表格优化 */
  .system-class-page .ant-tabs-tabpane[data-node-key="teachers"] .ant-table {
    min-width: 650px !important;
  }

  /* 家长表格优化 */
  .system-class-page .ant-tabs-tabpane[data-node-key="parents"] .ant-table {
    min-width: 700px !important;
  }

  /* 表格操作按钮优化 */
  .system-class-page .ant-table .ant-btn {
    padding: 2px 6px !important;
    font-size: 11px !important;
    height: 28px !important;
    margin: 1px !important;
  }

  /* 表格分页优化 */
  .system-class-page .ant-pagination {
    margin-top: 12px !important;
    text-align: center !important;
  }

  .system-class-page .ant-pagination-item,
  .system-class-page .ant-pagination-prev,
  .system-class-page .ant-pagination-next {
    min-width: 36px !important;
    height: 36px !important;
    line-height: 34px !important;
  }

  /* 移动端表格滚动提示 */
  .system-class-page .ant-table-wrapper::before {
    content: "👈 左右滑动查看更多列" !important;
    display: block !important;
    text-align: center !important;
    font-size: 12px !important;
    color: #999 !important;
    padding: 8px !important;
    background: #f8f9fa !important;
    border-radius: 4px !important;
    margin-bottom: 8px !important;
  }

  /* 桌面端隐藏滚动提示 */
  @media (min-width: 769px) {
    .system-class-page .ant-table-wrapper::before {
      display: none !important;
    }
  }

  /* 系统学校管理页面优化 */
  .system-school-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-school-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-school-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .system-school-page .ant-table {
    min-width: 900px !important;
  }

  /* 系统统计页面优化 */
  .system-statistics-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-statistics-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-statistics-page .ant-statistic {
    text-align: center !important;
  }

  .system-statistics-page .ant-statistic-title {
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }

  .system-statistics-page .ant-statistic-content {
    font-size: 24px !important;
    font-weight: 600 !important;
  }

  /* 系统作业分析页面优化 */
  .system-homework-analysis-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-homework-analysis-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-homework-analysis-page .chart-container {
    height: 300px !important;
    padding: 16px !important;
  }

  /* 系统错题训练页面优化 */
  .system-training-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .system-training-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .system-training-page .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .system-training-page .ant-table {
    min-width: 800px !important;
  }

  /* 超级管理员通用表格优化 */
  .super-admin-page .ant-table-thead > tr > th,
  .system-page .ant-table-thead > tr > th,
  .admin-system-page .ant-table-thead > tr > th {
    padding: 12px 8px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    background: #fafafa !important;
  }

  .super-admin-page .ant-table-tbody > tr > td,
  .system-page .ant-table-tbody > tr > td,
  .admin-system-page .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 13px !important;
    vertical-align: middle !important;
  }

  /* 超级管理员通用按钮优化 */
  .super-admin-page .ant-btn,
  .system-page .ant-btn,
  .admin-system-page .ant-btn {
    height: 44px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    margin: 4px !important;
    min-width: 80px !important;
  }

  .super-admin-page .ant-btn-primary,
  .system-page .ant-btn-primary,
  .admin-system-page .ant-btn-primary {
    font-weight: 500 !important;
  }

  /* 超级管理员通用表单优化 */
  .super-admin-page .ant-form-item,
  .system-page .ant-form-item,
  .admin-system-page .ant-form-item {
    margin-bottom: 20px !important;
  }

  .super-admin-page .ant-input,
  .system-page .ant-input,
  .admin-system-page .ant-input,
  .super-admin-page .ant-select-selector,
  .system-page .ant-select-selector,
  .admin-system-page .ant-select-selector {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  .super-admin-page .ant-input-number,
  .system-page .ant-input-number,
  .admin-system-page .ant-input-number {
    width: 100% !important;
    height: 44px !important;
  }

  .super-admin-page .ant-input-number-input,
  .system-page .ant-input-number-input,
  .admin-system-page .ant-input-number-input {
    height: 42px !important;
    font-size: 16px !important;
  }

  /* 超级管理员通用模态框优化 */
  .super-admin-page .ant-modal,
  .system-page .ant-modal,
  .admin-system-page .ant-modal {
    max-width: calc(100vw - 32px) !important;
    margin: 16px !important;
  }

  .super-admin-page .ant-modal-content,
  .system-page .ant-modal-content,
  .admin-system-page .ant-modal-content {
    border-radius: 12px !important;
    overflow: hidden !important;
  }

  .super-admin-page .ant-modal-header,
  .system-page .ant-modal-header,
  .admin-system-page .ant-modal-header {
    padding: 20px 24px 16px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .super-admin-page .ant-modal-title,
  .system-page .ant-modal-title,
  .admin-system-page .ant-modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
  }

  .super-admin-page .ant-modal-body,
  .system-page .ant-modal-body,
  .admin-system-page .ant-modal-body {
    padding: 20px 24px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .super-admin-page .ant-modal-footer,
  .system-page .ant-modal-footer,
  .admin-system-page .ant-modal-footer {
    padding: 16px 24px 20px !important;
    border-top: 1px solid #f0f0f0 !important;
    text-align: center !important;
  }

  .super-admin-page .ant-modal-footer .ant-btn,
  .system-page .ant-modal-footer .ant-btn,
  .admin-system-page .ant-modal-footer .ant-btn {
    margin: 0 8px !important;
    min-width: 100px !important;
  }

  /* 超级管理员通用分页优化 */
  .super-admin-page .ant-pagination,
  .system-page .ant-pagination,
  .admin-system-page .ant-pagination {
    text-align: center !important;
    margin-top: 20px !important;
    padding: 16px !important;
  }

  .super-admin-page .ant-pagination-item,
  .system-page .ant-pagination-item,
  .admin-system-page .ant-pagination-item,
  .super-admin-page .ant-pagination-prev,
  .system-page .ant-pagination-prev,
  .admin-system-page .ant-pagination-prev,
  .super-admin-page .ant-pagination-next,
  .system-page .ant-pagination-next,
  .admin-system-page .ant-pagination-next {
    min-width: 44px !important;
    height: 44px !important;
    line-height: 42px !important;
    margin: 0 4px !important;
    border-radius: 8px !important;
  }

  .super-admin-page .ant-pagination-jump-prev,
  .system-page .ant-pagination-jump-prev,
  .admin-system-page .ant-pagination-jump-prev,
  .super-admin-page .ant-pagination-jump-next,
  .system-page .ant-pagination-jump-next,
  .admin-system-page .ant-pagination-jump-next {
    min-width: 44px !important;
    height: 44px !important;
    line-height: 42px !important;
  }

  /* 数据库统计卡片优化 */
  .db-admin-page .stats-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 12px !important;
    margin-bottom: 16px !important;
  }

  .db-admin-page .stat-card {
    padding: 16px !important;
    border-radius: 12px !important;
    background: white !important;
    border: 1px solid #f0f0f0 !important;
    text-align: center !important;
  }

  .db-admin-page .stat-value {
    font-size: 24px !important;
    font-weight: 600 !important;
    color: #1890ff !important;
    margin-bottom: 4px !important;
  }

  .db-admin-page .stat-label {
    font-size: 12px !important;
    color: #666 !important;
  }

  /* 表格列表优化 */
  .db-admin-page .table-list {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }

  .db-admin-page .table-item {
    padding: 12px !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 8px !important;
    background: white !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }

  .db-admin-page .table-info {
    flex: 1 !important;
  }

  .db-admin-page .table-name {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 2px !important;
  }

  .db-admin-page .table-stats {
    font-size: 12px !important;
    color: #666 !important;
  }

  .db-admin-page .table-actions {
    display: flex !important;
    gap: 8px !important;
  }

  .db-admin-page .table-actions .ant-btn {
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
  }
}

/* ===== 系统设置移动端优化 ===== */
@media (max-width: 768px) {
  /* 系统设置页面优化 */
  .system-settings-page {
    padding: 12px 4px !important;
  }

  .system-settings-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
  }

  .system-settings-page .ant-card-body {
    padding: 16px !important;
  }

  /* 设置表单优化 */
  .system-settings-page .ant-form-item {
    margin-bottom: 16px !important;
  }

  .system-settings-page .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .system-settings-page .ant-form-item-label > label {
    font-size: 14px !important;
    font-weight: 500 !important;
  }

  .system-settings-page .ant-switch {
    margin-right: 8px !important;
  }

  .system-settings-page .ant-input,
  .system-settings-page .ant-select-selector,
  .system-settings-page .ant-picker {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
  }

  .system-settings-page .ant-input-number {
    width: 100% !important;
    height: 44px !important;
  }

  .system-settings-page .ant-input-number-input {
    height: 42px !important;
    font-size: 16px !important;
  }

  /* 设置分组优化 */
  .system-settings-page .settings-group {
    margin-bottom: 24px !important;
    padding: 16px !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 12px !important;
    background: #fafafa !important;
  }

  .system-settings-page .settings-group-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 12px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e8e8e8 !important;
  }

  /* 操作按钮优化 */
  .system-settings-page .action-buttons {
    display: flex !important;
    gap: 12px !important;
    justify-content: center !important;
    margin-top: 24px !important;
  }

  .system-settings-page .action-buttons .ant-btn {
    height: 44px !important;
    font-size: 16px !important;
    padding: 0 24px !important;
    border-radius: 8px !important;
    flex: 1 !important;
    max-width: 150px !important;
  }
}

/* ===== 家长角色移动端优化 ===== */
@media (max-width: 768px) {
  /* 家长页面基础优化 */
  .parent-page {
    padding: 12px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .parent-page .ant-card {
    margin-bottom: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-page .ant-card-body {
    padding: 16px !important;
  }

  /* 家长报告页面优化 */
  .parent-report-page {
    padding: 12px 4px !important;
  }

  .parent-report-page .report-header {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
    padding: 16px !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-report-page .report-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    text-align: center !important;
    margin: 0 !important;
  }

  .parent-report-page .report-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
  }

  .parent-report-page .student-selector {
    width: 100% !important;
    height: 44px !important;
  }

  .parent-report-page .student-selector .ant-select-selector {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  .parent-report-page .report-actions {
    display: flex !important;
    gap: 8px !important;
  }

  .parent-report-page .report-actions .ant-btn {
    flex: 1 !important;
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  /* 家长报告内容优化 */
  .parent-report-page .report-section {
    margin-bottom: 16px !important;
    padding: 16px !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-report-page .section-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 12px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .parent-report-page .section-content {
    font-size: 14px !important;
    line-height: 1.6 !important;
    color: #595959 !important;
  }

  /* 家长端作业详情页面优化 */
  .parent-homework-detail-page {
    padding: 8px 4px !important;
    background: #f5f5f5 !important;
    min-height: 100vh !important;
  }

  .parent-homework-detail-page .ant-card {
    margin-bottom: 12px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-homework-detail-page .ant-card-body {
    padding: 12px !important;
  }

  /* 头部导航优化 */
  .parent-homework-detail-page .homework-header {
    padding: 12px !important;
    margin-bottom: 12px !important;
  }

  .parent-homework-detail-page .homework-header .ant-typography-title {
    font-size: 18px !important;
    margin: 0 !important;
  }

  .parent-homework-detail-page .homework-header .ant-typography {
    font-size: 13px !important;
  }

  /* 统计卡片优化 */
  .parent-homework-detail-page .stats-row .ant-col {
    margin-bottom: 8px !important;
  }

  .parent-homework-detail-page .stats-row .ant-card {
    margin-bottom: 0 !important;
  }

  .parent-homework-detail-page .stats-row .ant-statistic-title {
    font-size: 12px !important;
  }

  .parent-homework-detail-page .stats-row .ant-statistic-content {
    font-size: 16px !important;
  }

  /* 作业列表优化 */
  .parent-homework-detail-page .homework-list .ant-list-item {
    padding: 8px 0 !important;
  }

  .parent-homework-detail-page .homework-list .ant-card {
    margin-bottom: 0 !important;
  }

  .parent-homework-detail-page .homework-list .ant-card-body {
    padding: 12px !important;
  }

  /* 作业项标题优化 */
  .parent-homework-detail-page .homework-item-title {
    font-size: 16px !important;
    margin: 0 0 8px 0 !important;
    line-height: 1.3 !important;
  }

  /* 状态标签优化 - 横向排列 */
  .parent-homework-detail-page .homework-status-tags {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 6px !important;
    margin-bottom: 12px !important;
  }

  .parent-homework-detail-page .homework-status-tags .ant-tag {
    margin: 0 !important;
    font-size: 11px !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
  }

  /* 描述信息优化 - 紧凑布局 */
  .parent-homework-detail-page .homework-descriptions {
    margin-bottom: 12px !important;
  }

  .parent-homework-detail-page .homework-descriptions .ant-descriptions-item {
    padding-bottom: 4px !important;
  }

  .parent-homework-detail-page .homework-descriptions .ant-descriptions-item-label {
    font-size: 12px !important;
    color: #8c8c8c !important;
    width: 60px !important;
  }

  .parent-homework-detail-page .homework-descriptions .ant-descriptions-item-content {
    font-size: 12px !important;
  }

  /* 时间显示优化 - 简化格式 */
  .parent-homework-detail-page .homework-time {
    font-size: 11px !important;
    color: #8c8c8c !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
  }

  .parent-homework-detail-page .homework-time .anticon {
    font-size: 10px !important;
  }

  /* 成绩区域优化 */
  .parent-homework-detail-page .homework-scores {
    margin-top: 12px !important;
  }

  .parent-homework-detail-page .homework-scores .ant-card {
    margin-bottom: 8px !important;
  }

  .parent-homework-detail-page .homework-scores .ant-statistic-title {
    font-size: 11px !important;
  }

  .parent-homework-detail-page .homework-scores .ant-statistic-content {
    font-size: 14px !important;
  }

  /* 老师评语优化 */
  .parent-homework-detail-page .homework-comment {
    margin-top: 8px !important;
  }

  .parent-homework-detail-page .homework-comment .ant-card-head {
    padding: 8px 12px !important;
    min-height: auto !important;
  }

  .parent-homework-detail-page .homework-comment .ant-card-head-title {
    font-size: 12px !important;
  }

  .parent-homework-detail-page .homework-comment .ant-card-body {
    padding: 8px 12px !important;
  }

  .parent-homework-detail-page .homework-comment .ant-typography {
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  /* 分页优化 */
  .parent-homework-detail-page .homework-pagination {
    text-align: center !important;
    margin-top: 16px !important;
  }

  .parent-homework-detail-page .homework-pagination .ant-pagination {
    font-size: 12px !important;
  }

  .parent-homework-detail-page .homework-pagination .ant-pagination-item {
    min-width: 28px !important;
    height: 28px !important;
    line-height: 26px !important;
  }

  /* 家长注册页面优化 */
  .parent-registration-page {
    padding: 12px 4px !important;
  }

  .parent-registration-page .registration-header {
    text-align: center !important;
    margin-bottom: 24px !important;
    padding: 20px 16px !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-registration-page .registration-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 8px !important;
  }

  .parent-registration-page .registration-description {
    font-size: 14px !important;
    color: #8c8c8c !important;
    line-height: 1.5 !important;
  }

  /* 家长搜索表单优化 */
  .parent-registration-page .search-form {
    margin-bottom: 20px !important;
    padding: 16px !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }

  .parent-registration-page .search-form .ant-form-item {
    margin-bottom: 16px !important;
  }

  .parent-registration-page .search-form .ant-input {
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  .parent-registration-page .search-form .ant-btn {
    width: 100% !important;
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
  }

  /* 学生列表卡片优化 */
  .parent-registration-page .student-list {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .parent-registration-page .student-card {
    padding: 16px !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f0f0f0 !important;
  }

  .parent-registration-page .student-card:active {
    background: #f8f9fa !important;
    transform: scale(0.98) !important;
    transition: all 0.1s ease !important;
  }

  .parent-registration-page .student-info {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 12px !important;
  }

  .parent-registration-page .student-name {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
    margin-bottom: 4px !important;
  }

  .parent-registration-page .student-details {
    font-size: 13px !important;
    color: #8c8c8c !important;
  }

  .parent-registration-page .bind-button {
    width: 100% !important;
    height: 40px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    background: #1890ff !important;
    border-color: #1890ff !important;
    color: white !important;
  }
}

/* ===== 通用组件移动端优化 ===== */
@media (max-width: 768px) {
  /* 通用表格优化 */
  .ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
  }

  .ant-table {
    min-width: 600px !important;
  }

  .ant-table-thead > tr > th {
    padding: 12px 8px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    background: #fafafa !important;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
    font-size: 13px !important;
    vertical-align: middle !important;
  }

  .ant-table-tbody .ant-btn {
    min-width: 60px !important;
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    border-radius: 4px !important;
    touch-action: manipulation !important;
    margin: 1px !important;
  }

  .ant-table-tbody .ant-btn:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }

  /* 通用分页优化 */
  .ant-pagination {
    margin: 16px 0 !important;
    text-align: center !important;
  }

  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next {
    min-width: 36px !important;
    height: 36px !important;
    line-height: 34px !important;
    margin: 0 2px !important;
  }

  .ant-pagination-options {
    display: none !important; /* 移动端隐藏分页选项 */
  }

  .ant-pagination-total-text {
    display: none !important; /* 移动端隐藏总数文字 */
  }

  /* 通用卡片优化 */
  .ant-card {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    margin-bottom: 16px !important;
  }

  .ant-card-head {
    padding: 16px 20px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .ant-card-head-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #262626 !important;
  }

  .ant-card-body {
    padding: 16px 20px !important;
  }

  .ant-card-actions {
    background: #fafafa !important;
    border-top: 1px solid #f0f0f0 !important;
  }

  .ant-card-actions > li {
    margin: 8px 0 !important;
  }

  .ant-card-actions > li > span {
    font-size: 14px !important;
  }
}

/* ===== 通用表单组件移动端优化 ===== */
@media (max-width: 768px) {
  /* 表单项优化 */
  .ant-form-item {
    margin-bottom: 16px !important;
  }

  .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .ant-form-item-label > label {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #262626 !important;
  }

  .ant-form-item-required::before {
    color: #ff4d4f !important;
  }

  /* 输入框优化 */
  .ant-input {
    height: 44px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 6px !important;
    padding: 0 12px !important;
  }

  .ant-input:focus,
  .ant-input-focused {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  }

  /* 文本域优化 */
  .ant-input {
    min-height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
    padding: 12px !important;
    line-height: 1.5 !important;
  }

  /* 选择器优化 */
  .ant-select {
    min-height: 44px !important;
  }

  .ant-select-selector {
    height: 44px !important;
    padding: 8px 12px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
  }

  .ant-select-selection-item {
    line-height: 28px !important;
    font-size: 16px !important;
  }

  .ant-select-selection-placeholder {
    line-height: 28px !important;
    font-size: 16px !important;
    color: #bfbfbf !important;
  }

  /* 日期选择器优化 */
  .ant-picker {
    width: 100% !important;
    height: 44px !important;
    font-size: 16px !important;
    border-radius: 6px !important;
  }

  .ant-picker-input > input {
    font-size: 16px !important;
    height: 42px !important;
  }

  /* 数字输入框优化 */
  .ant-input-number {
    width: 100% !important;
    height: 44px !important;
  }

  .ant-input-number-input {
    height: 42px !important;
    font-size: 16px !important;
    padding: 0 12px !important;
  }

  /* 开关优化 */
  .ant-switch {
    min-width: 44px !important;
    height: 22px !important;
  }

  /* 复选框优化 */
  .ant-checkbox-wrapper {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .ant-checkbox {
    transform: scale(1.2) !important;
    margin-right: 8px !important;
  }

  /* 单选框优化 */
  .ant-radio-wrapper {
    font-size: 16px !important;
    line-height: 1.5 !important;
    margin-bottom: 8px !important;
  }

  .ant-radio {
    transform: scale(1.2) !important;
    margin-right: 8px !important;
  }
}

/* ===== 通用模态框和抽屉优化 ===== */
@media (max-width: 768px) {
  /* 模态框优化 */
  .ant-modal {
    margin: 0 !important;
    max-width: calc(100vw - 16px) !important;
    top: 20px !important;
  }

  .ant-modal-content {
    border-radius: 12px !important;
    max-height: calc(100vh - 40px) !important;
    overflow: hidden !important;
  }

  .ant-modal-header {
    padding: 16px 20px !important;
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .ant-modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #262626 !important;
  }

  .ant-modal-body {
    padding: 20px !important;
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-modal-footer {
    padding: 12px 20px !important;
    text-align: center !important;
    border-top: 1px solid #f0f0f0 !important;
  }

  .ant-modal-footer .ant-btn {
    height: 44px !important;
    font-size: 16px !important;
    padding: 0 24px !important;
    margin: 0 8px !important;
    border-radius: 8px !important;
  }

  /* 抽屉优化 */
  .ant-drawer-content {
    border-radius: 12px 0 0 12px !important;
  }

  .ant-drawer-header {
    padding: 16px 20px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .ant-drawer-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #262626 !important;
  }

  .ant-drawer-body {
    padding: 20px !important;
  }

  /* 确认框优化 */
  .ant-popconfirm {
    max-width: calc(100vw - 32px) !important;
  }

  .ant-popconfirm-inner-content {
    padding: 16px !important;
  }

  .ant-popconfirm-buttons {
    margin-top: 12px !important;
    text-align: center !important;
  }

  .ant-popconfirm-buttons .ant-btn {
    height: 36px !important;
    font-size: 14px !important;
    padding: 0 16px !important;
    margin: 0 4px !important;
  }
}

/* ===== 通用上传组件移动端优化 ===== */
@media (max-width: 768px) {
  /* 文件上传优化 */
  .ant-upload-wrapper {
    width: 100% !important;
  }

  .ant-upload {
    width: 100% !important;
  }

  .ant-upload-drag {
    border-radius: 12px !important;
    border: 2px dashed #d9d9d9 !important;
    background: #fafafa !important;
    padding: 20px !important;
  }

  .ant-upload-drag:hover {
    border-color: #1890ff !important;
    background: #f0f8ff !important;
  }

  .ant-upload-drag-icon {
    font-size: 48px !important;
    color: #d9d9d9 !important;
    margin-bottom: 16px !important;
  }

  .ant-upload-text {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #262626 !important;
    margin-bottom: 8px !important;
  }

  .ant-upload-hint {
    font-size: 14px !important;
    color: #8c8c8c !important;
  }

  /* 图片上传卡片优化 */
  .ant-upload-list-picture-card .ant-upload-list-item {
    width: 100px !important;
    height: 100px !important;
    margin: 0 8px 8px 0 !important;
  }

  .ant-upload-list-picture-card-container {
    width: 100px !important;
    height: 100px !important;
  }

  .ant-upload-select-picture-card {
    width: 100px !important;
    height: 100px !important;
    border-radius: 8px !important;
  }

  /* 文件列表优化 */
  .ant-upload-list-item {
    padding: 8px 12px !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
  }

  .ant-upload-list-item-name {
    font-size: 14px !important;
    padding-left: 0 !important;
  }

  .ant-upload-list-item-actions {
    right: 8px !important;
  }

  .ant-upload-list-item-actions .anticon {
    font-size: 16px !important;
    padding: 4px !important;
  }
}

/* ===== 通用标签页和导航优化 ===== */
@media (max-width: 768px) {
  /* 标签页优化 */
  .ant-tabs {
    overflow: hidden !important;
  }

  .ant-tabs-nav {
    margin-bottom: 16px !important;
  }

  .ant-tabs-nav-wrap {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-tabs-nav-list {
    display: flex !important;
    flex-wrap: nowrap !important;
  }

  .ant-tabs-tab {
    padding: 12px 16px !important;
    font-size: 14px !important;
    margin: 0 4px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
  }

  .ant-tabs-tab-btn {
    font-weight: 500 !important;
  }

  .ant-tabs-content-holder {
    padding: 0 !important;
  }

  .ant-tabs-tabpane {
    padding: 0 !important;
  }

  /* 卡片式标签页优化 */
  .ant-tabs-card .ant-tabs-tab {
    border-radius: 8px 8px 0 0 !important;
    background: #f5f5f5 !important;
    border: 1px solid #d9d9d9 !important;
    margin: 0 2px !important;
  }

  .ant-tabs-card .ant-tabs-tab-active {
    background: white !important;
    border-bottom-color: white !important;
  }

  /* 面包屑优化 */
  .ant-breadcrumb {
    margin-bottom: 16px !important;
    font-size: 14px !important;
  }

  .ant-breadcrumb-link {
    color: #8c8c8c !important;
  }

  .ant-breadcrumb-separator {
    margin: 0 4px !important;
  }

  /* 步骤条优化 */
  .ant-steps {
    margin-bottom: 24px !important;
  }

  .ant-steps-item {
    padding-left: 0 !important;
  }

  .ant-steps-item-title {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  .ant-steps-item-description {
    font-size: 12px !important;
    color: #8c8c8c !important;
  }

  /* 小型步骤条 */
  .ant-steps-small .ant-steps-item-title {
    font-size: 13px !important;
  }

  .ant-steps-small .ant-steps-item-description {
    font-size: 11px !important;
  }
}

/* ===== 通用消息和提示优化 ===== */
@media (max-width: 768px) {
  /* 消息提示优化 */
  .ant-message {
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: calc(100vw - 32px) !important;
    max-width: 400px !important;
  }

  .ant-message-notice {
    padding: 12px 16px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-message-notice-content {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  /* 通知提示优化 */
  .ant-notification {
    width: calc(100vw - 32px) !important;
    max-width: 400px !important;
    margin-right: 16px !important;
  }

  .ant-notification-notice {
    padding: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-notification-notice-message {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
  }

  .ant-notification-notice-description {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* 警告提示优化 */
  .ant-alert {
    padding: 12px 16px !important;
    border-radius: 8px !important;
    margin-bottom: 16px !important;
  }

  .ant-alert-message {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 4px !important;
  }

  .ant-alert-description {
    font-size: 13px !important;
    line-height: 1.5 !important;
  }

  .ant-alert-action {
    margin-left: 8px !important;
  }

  .ant-alert-close-icon {
    font-size: 16px !important;
    padding: 4px !important;
  }
}

/* ===== 通用统计和标签优化 ===== */
@media (max-width: 768px) {
  /* 统计数值优化 */
  .ant-statistic {
    text-align: center !important;
  }

  .ant-statistic-title {
    font-size: 13px !important;
    color: #8c8c8c !important;
    margin-bottom: 8px !important;
  }

  .ant-statistic-content {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #262626 !important;
  }

  .ant-statistic-content-value {
    font-size: 20px !important;
  }

  .ant-statistic-content-suffix {
    font-size: 16px !important;
    margin-left: 4px !important;
  }

  /* 标签优化 */
  .ant-tag {
    margin: 0 4px 4px 0 !important;
    padding: 2px 8px !important;
    font-size: 12px !important;
    border-radius: 4px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
  }

  .ant-tag-checkable {
    padding: 4px 12px !important;
    font-size: 13px !important;
    border-radius: 6px !important;
  }

  .ant-tag-checkable:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
  }

  /* 进度条优化 */
  .ant-progress {
    margin-bottom: 16px !important;
  }

  .ant-progress-text {
    font-size: 13px !important;
    font-weight: 500 !important;
  }

  .ant-progress-circle {
    width: 80px !important;
    height: 80px !important;
  }

  .ant-progress-circle .ant-progress-text {
    font-size: 14px !important;
  }

  /* 空状态优化 */
  .ant-empty {
    padding: 40px 20px !important;
  }

  .ant-empty-image {
    height: 80px !important;
    margin-bottom: 16px !important;
  }

  .ant-empty-description {
    font-size: 14px !important;
    color: #8c8c8c !important;
    margin-bottom: 16px !important;
  }

  .ant-empty-footer {
    margin-top: 16px !important;
  }

  .ant-empty-footer .ant-btn {
    height: 40px !important;
    font-size: 14px !important;
    padding: 0 20px !important;
  }

  /* ===== 作业分析页面移动端优化 ===== */
  /* 隐藏侧边栏，使用顶部导航 */
  .homework-analysis-page .ant-layout-sider,
  .system-homework-analysis-page .ant-layout-sider {
    display: none !important;
  }

  .homework-analysis-page .ant-layout,
  .system-homework-analysis-page .ant-layout {
    padding: 0 !important;
    background: #f5f5f5 !important;
  }

  .homework-analysis-page .ant-layout-content,
  .system-homework-analysis-page .ant-layout-content {
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
  }

  /* 作业管理页面移动端优化 - 隐藏侧边栏，显示顶部导航 */
  .homework-management-page .ant-layout-sider,
  .system-homework-management-page .ant-layout-sider {
    display: none !important;
  }

  .homework-management-page .ant-layout,
  .system-homework-management-page .ant-layout {
    background: #f5f5f5 !important;
  }

  .homework-management-page .ant-layout-content,
  .system-homework-management-page .ant-layout-content {
    padding: 0 !important;
    background: transparent !important;
  }

  /* 移动端顶部导航替代侧边栏 */
  .homework-management-page .mobile-top-nav,
  .system-homework-management-page .mobile-top-nav {
    display: block !important;
    background: white !important;
    padding: 12px 16px 0 16px !important;
    margin: 12px 4px 16px 4px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 2px solid #1890ff !important; /* 调试用边框 */
    position: relative !important;
    z-index: 100 !important;
    width: calc(100% - 8px) !important;
    min-height: 60px !important; /* 确保有足够高度 */
  }

  .homework-management-page .mobile-top-nav .ant-tabs,
  .system-homework-management-page .mobile-top-nav .ant-tabs {
    margin-bottom: 0 !important;
  }

  .homework-management-page .mobile-top-nav .ant-tabs-tab,
  .system-homework-management-page .mobile-top-nav .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 13px !important;
    margin: 0 4px !important;
    display: inline-flex !important;
    align-items: center !important;
  }

  .homework-management-page .mobile-top-nav .ant-tabs-tab-btn,
  .system-homework-management-page .mobile-top-nav .ant-tabs-tab-btn {
    white-space: nowrap !important;
    font-weight: 500 !important;
  }

  .homework-management-page .mobile-top-nav .ant-tabs-ink-bar,
  .system-homework-management-page .mobile-top-nav .ant-tabs-ink-bar {
    background: #1890ff !important;
    height: 3px !important;
  }

  /* 移动端内容区域调整 */
  .homework-management-page .mobile-content,
  .system-homework-management-page .mobile-content {
    padding: 0 4px 12px 4px !important;
  }

  /* 作业分析页面移动端导航样式 */
  .homework-analysis-page .mobile-nav-header,
  .system-homework-analysis-page .mobile-nav-header {
    background: white !important;
    padding: 16px !important;
    margin: 12px 4px 16px 4px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    position: relative !important;
    z-index: 10 !important;
    width: calc(100% - 8px) !important;
  }

  .homework-analysis-page .mobile-nav-header h3,
  .system-homework-analysis-page .mobile-nav-header h3 {
    margin: 0 0 12px 0 !important;
    font-size: 18px !important;
    color: #1890ff !important;
    text-align: center !important;
  }

  .homework-analysis-page .mobile-nav-tabs,
  .system-homework-analysis-page .mobile-nav-tabs {
    margin-bottom: 0 !important;
  }

  .homework-analysis-page .mobile-nav-tabs .ant-tabs-tab,
  .system-homework-analysis-page .mobile-nav-tabs .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 13px !important;
    margin: 0 4px !important;
  }

  .homework-analysis-page .mobile-nav-tabs .ant-tabs-tab-btn,
  .system-homework-analysis-page .mobile-nav-tabs .ant-tabs-tab-btn {
    white-space: nowrap !important;
    font-weight: 500 !important;
  }

  .homework-analysis-page .mobile-nav-tabs .ant-tabs-ink-bar,
  .system-homework-analysis-page .mobile-nav-tabs .ant-tabs-ink-bar {
    background: #1890ff !important;
    height: 3px !important;
  }
}
